import FpRequest from '../server'
import config from './config'
const API = `${config.BASE_API}`

// discord玩家池列表
export const playerDCPoll: ApiT = params => FpRequest.post(`${API}/dsc/user/pool`, params)
// discord机器人列表
export const robotList: ApiT = params => FpRequest.post(`${API}/addons/dsc_bot_list`, params)
// 拉取对话历史消息
export const getMessageList: ApiT = params => FpRequest.post(`${API}/dsc/channel/dialog`, params)
// 发送消息
export const sendDcMessage: ApiT = params => FpRequest.post(`${API}/dsc/channel/message_create`, params)
// 拉取新消息+处理事件
export const getNewMessageEvent: ApiT = params => FpRequest.post(`${API}/dsc/channel/dialog_fresh`, params)
// 获取代理资源
export const getProxyResource: ApiT = params => FpRequest.post(`${API}/dsc/proxy/resource`, params)
// 工单数据总览
export const getStats: ApiT = params => FpRequest.post(`${API}/dsc/user/stats`, params)
// 新增/编辑玩家画像
export const addPortrait: ApiT = params => FpRequest.post(`${API}/dsc/portrait/edit`, params)
// 获取玩家画像
export const getPortrait: ApiT = params => FpRequest.post(`${API}/dsc/portrait/info`, params)
// 下载
export const discordDown: ApiT = params => FpRequest.download(`${API}/dsc/user/pool/export`, 'post', params)
// 批量私信
// export const pathMessage: ApiT = params => FpRequest.post(`${API}/dsc/channel/batch/message_create`, params)
export const pathMessage: ApiT = params => FpRequest.post(`${API}/dsc/channel/batch/async_message_create`, params)
// 批量私信列表信息
export const pathMessageList: ApiT = params => FpRequest.post(`${API}/dsc/message/task/list`, params)
// 批量私信-数据导出
export const pathAllDown: ApiT = params => FpRequest.download(`${API}/dsc/message/task/list/export`, 'post', params)
// 批量私信-下载明细
export const pathDown: ApiT = params => FpRequest.download(`${API}/dsc/message/task/detail/export`, 'post', params)
// 批量发送文件
export const sedPathFile: ApiT = params => FpRequest.post(`${API}/dsc/channel/batch/send_file`, params)
// 新增discord沟通记录  注意：line用line的 这个改造的新接口后端两张表 获取沟通对话有问题
export const addCommunicate: ApiT = params => FpRequest.post(`${API}/dsc/new/commu/save`, params)
// 添加备注
export const addMark: ApiT = params => FpRequest.post(`${API}/dsc/user/remark/save`, params)
// 添加Tab
export const addDiscordTab: ApiT = params => FpRequest.post(`${API}/dsc/tab/save`, params)
// 编辑Tab
export const editDiscordTab: ApiT = params => FpRequest.post(`${API}/dsc/tab/edit`, params)
// 搜索tab列表
export const fetchTabLists: ApiT = params => FpRequest.post(`${API}/dsc/tab/list`, params)
// 设置搜索tab排序
export const updateTabSettingOrder: ApiT = params => FpRequest.post(`${API}/dsc/tab/update_sort`, params)
// 删除tab项
export const deleteTab: ApiT = params => FpRequest.post(`${API}/dsc/tab/del`, params)
// discord标签库列表
export const discordTagList: ApiT = params => FpRequest.post(`${API}/dsc/tag/list`, params)
// 批量打标签
export const batchAddTag: ApiT = params => FpRequest.post(`${API}/dsc/batch-tag`, params)
// disocrd 客服编辑消息
export const discordMessageEdit: ApiT = params => FpRequest.post(`${API}/dsc/channel/message_edit`, params)
// disocrd回复玩家消息
export const discordReplyMessage: ApiT = params => FpRequest.post(`${API}/dsc/channel/message_reply`, params)
// disocrd修正客服对玩家消息的回复状态
export const discordReplyStatus: ApiT = params => FpRequest.post(`${API}/dsc/reply_status/rectify`, params)
// 获取标签列表-lib_type传值，工单 1，DC 2，Line 3
export const getTagList: ApiT = params => FpRequest.post(`${API}/new/tags/opts`, params)
// 自定义tab数量
export const getTabCount: ApiT = params => FpRequest.post(`${API}/dsc/tab/count`, params)

// 获取DC公共标签
export const getDcCommonTag: ApiT = params => FpRequest.post(`${API}/dsc/tag/public`, params)
// 批量删除DC标签
export const batchDeleteDCTag: ApiT = params => FpRequest.post(`${API}/dsc/tag/batch_delete`, params)