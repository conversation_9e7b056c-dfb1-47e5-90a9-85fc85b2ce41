import FpRequest from '../server';
import config from './config';
const API = `${config.BASE_API}`;

// ========== 统一邮件API (UnifiedMailAPI) ==========
// 获取邮件会话列表
export const getThreadsList: ApiT = params => FpRequest.post(`${API}/v2/threads/list`, params);
// 获取单个会话详情
export const getThreadDetail: ApiT = params => FpRequest.post(`${API}/v2/threads/detail`, params);
// 回复会话中的邮件
export const replyThreadMessage: ApiT = params =>
  FpRequest.post(`${API}/v2/threads/sync/reply`, params);
// 更新会话状态
export const updateThreadStatus: ApiT = params =>
  FpRequest.post(`${API}/v2/threads/status`, params);
// 标记会话为已读
export const markThreadAsRead: ApiT = params => FpRequest.post(`${API}/threads/read`, params);
// 同步所有会话
export const syncAllThreads: ApiT = params => FpRequest.post(`${API}/threads/sync`, params);
// 邮件指派
export const assignEmail: ApiT = params => FpRequest.post(`${API}/v2/threads/reassign`, params);
// 批量邮件指派
export const batchAssignEmail: ApiT = params =>
  FpRequest.post(`${API}/v2/threads/batch_reassign`, params);
// 更新邮件备注
export const updateEmailRemark: ApiT = params => FpRequest.post(`${API}/v2/threads/remark`, params);
// 获取绑定标签
export const getEmailBindTags: ApiT = params => FpRequest.post(`${API}/v2/threads/tags`, params);
// 更新邮件标签
export const updateEmailTags: ApiT = params => FpRequest.post(`${API}/v2/threads/tag`, params);
// 获取邮件标签选项
export const getEmailTagOptions: ApiT = params =>
  FpRequest.post(`${API}/v2/threads/tag_options`, params);
// 获取邮件统计数据
export const getMailStats: ApiT = params => FpRequest.post(`${API}/v2/threads/count`, params);

// ========== 原有邮件API (保持兼容) ==========
// mail玩家查询结果下载接口
export const mailExportDown: ApiT = params =>
  FpRequest.download(`${API}/v2/threads/export`, 'post', params);
// mail玩家数据概览接口
export const getStats: ApiT = params => FpRequest.post(`${API}/line/user/stats`, params);
// mail发送消息接口
export const mailSendMsg: ApiT = params =>
  FpRequest.post(`${API}/line/channel/send_message`, params);
// mail历史聊天记录接口
export const mailMessageList: ApiT = params =>
  FpRequest.post(`${API}/line/channel/dialogue`, params);
// mail拉取新消息接口
export const mailNewMessage: ApiT = params =>
  FpRequest.post(`${API}/line/channel/dialogue_refresh`, params);
// 获取玩家画像信息
export const mailPortrait: ApiT = params => FpRequest.post(`${API}/line/portrait/info`, params);
// 新增/编辑 玩家画像标签信息
export const mailPortraitTag: ApiT = params =>
  FpRequest.post(`${API}/line/portrait/edit_tag`, params);
// 新增/编辑 玩家画像基础信息
export const mailPortraitBasic: ApiT = params =>
  FpRequest.post(`${API}/line/portrait/edit_basic`, params);
// 新增/编辑 玩家画像备注信息
export const mailPortraitRemark: ApiT = params =>
  FpRequest.post(`${API}/line/portrait/edit_remark`, params);
// 新增line沟通记录
//export const mailAddCommunicate: ApiT = params => FpRequest.post(`${API}/line/commu/save`, params)
// mail频道列表（根据游戏变化）
export const mailChannelList: ApiT = params =>
  FpRequest.post(`${API}/addons/line_channel_list`, params);
// 添加备注
export const mailAddMark: ApiT = params => FpRequest.post(`${API}/line/user/remark/save`, params);
// 添加Tab
export const addMailTab: ApiT = params => FpRequest.post(`${API}/v2/mail/tab/save`, params);
// 编辑Tab
export const editMailTab: ApiT = params => FpRequest.post(`${API}/v2/mail/tab/edit`, params);
// 删除tab项
export const delMailTab: ApiT = params => FpRequest.post(`${API}/v2/mail/tab/del`, params);
// 搜索tab列表
export const mailTabList: ApiT = params => FpRequest.post(`${API}/v2/mail/tab/list`, params);
// 获取tab数量
export const mailTabCount: ApiT = params => FpRequest.post(`${API}/v2/mail/tab/count`, params);
// 设置搜索tab排序
export const mailUpdateTabSettingOrder: ApiT = params =>
  FpRequest.post(`${API}/v2/mail/tab/update_sort`, params);
// 邮箱配置列表 email-configs/list
export const mailEmailConfigsList: ApiT = params =>
  FpRequest.post(`${API}/v2/email-configs/list`, params);
