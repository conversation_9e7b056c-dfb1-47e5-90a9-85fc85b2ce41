<template>
  <div class="custom-tab-overview">
    <div class="title">
      <span class="iconfont icon-duoweidu<PERSON><PERSON><PERSON><PERSON><PERSON>"></span>
      <span class="text">{{ $t('text_custom_data_overview') }}</span>
    </div>
    <el-scrollbar v-loading="tabsLoading">
      <el-menu
        v-if="openIndex.length > 0"
        :default-openeds="openIndex"
        class="project-sortable-tree"
      >
        <template v-for="(v, k) in tabList" :key="k">
          <el-sub-menu :index="v.project">
            <template #title>
              <DragIcon />
              <span>{{ v.project }}</span>
            </template>
            <template v-for="(l, m) in v.tabs" :key="m">
              <el-menu-item :index="l.tab_name" :data-id="l.id">
                <el-button
                  @click="handleTabClick(l)"
                  :class="[l.id === activeTabId ? 'activeBtn' : '']"
                  style="position: relative"
                >
                  <DragIcon />
                  {{ l.tab_name }}:{{ l.count }}
                  <el-link
                    :disabled="l.operator !== userInfo.username"
                    icon="Edit"
                    :underline="false"
                    class="edit-btn"
                    @click.stop="handleTabEdit(l)"
                  ></el-link>
                  <el-link
                    :disabled="l.operator !== userInfo.username"
                    icon="Delete"
                    :underline="false"
                    class="delete-btn"
                    @click.stop="handleTabDelete(l)"
                  ></el-link>
                </el-button>
              </el-menu-item>
            </template>
          </el-sub-menu>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import DragIcon from '@/components/DragIcon.vue';
import { useI18n } from 'vue-i18n';
import type { ProjectTab, TabItem } from '../types/email';

// 移除本地接口定义，使用导入的类型

interface Props {
  tabList: ProjectTab[];
  tabsLoading: boolean;
  openIndex: string[];
  activeTabId: number;
  userInfo: Record<string, unknown>;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (event: 'tab-click', tab: TabItem): void;
  (event: 'tab-edit', tab: TabItem): void;
  (event: 'tab-delete', tab: TabItem): void;
}>();

const { t: $t } = useI18n();

const handleTabClick = (tab: TabItem) => {
  emit('tab-click', tab);
};

const handleTabEdit = (tab: TabItem) => {
  emit('tab-edit', tab);
};

const handleTabDelete = (tab: TabItem) => {
  emit('tab-delete', tab);
};
</script>

<style lang="scss" scoped>
.custom-tab-overview {
  height: 100%;
  display: flex;
  flex-direction: column;

  .title {
    font-size: 18px;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    background-image: linear-gradient(to right, rgba(39, 177, 236, 0.8), rgba(74, 161, 129, 0.8));
    box-shadow: 0px 2px 7px -3px rgba(0, 0, 0, 0.6);
    margin-bottom: 20px;

    .text {
      margin-left: 10px;
    }
  }

  .el-scrollbar {
    height: calc(100% - 62px);
  }

  :deep(.el-sub-menu__title) {
    height: 40px;
    line-height: 40px;
    &:hover {
      background-color: transparent;
    }
  }

  :deep(.el-menu-item) {
    padding: 0;
    &:hover {
      background-color: transparent;
    }
  }

  .delete-btn {
    padding: 5px;
    position: absolute;
    right: -10px;
    visibility: hidden;
  }

  .edit-btn {
    position: absolute;
    right: 20px;
    visibility: hidden;
  }

  :deep(.el-button) {
    &:hover {
      .delete-btn,
      .edit-btn {
        visibility: visible !important;
        color: #4aa181;
        background-color: #edf6f2;
        transition: background-color 0.2s;
      }
    }
  }

  .activeBtn {
    color: var(--el-button-hover-text-color);
    border-color: var(--el-button-hover-border-color);
    background-color: var(--el-button-hover-bg-color);
    outline: none;
  }

  .el-button {
    display: block;
    margin: 2px auto 20px;
    width: 90%;
    overflow: hidden;
  }
}
</style>
