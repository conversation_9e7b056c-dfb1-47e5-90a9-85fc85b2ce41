<template>
  <el-scrollbar class="pane-wapper">
    <el-form
      size="small"
      ref="searchFormRef"
      :inline="true"
      :model="searchForm"
      class="search-form"
    >
      <!-- 邮箱筛选 -->
      <el-form-item :label="$t('text_email')">
        <el-select
          v-model="searchForm.email_id"
          filterable
          :placeholder="$t('place_select') + $t('text_email')"
          style="width: 200px"
          clearable
        >
          <el-option
            v-for="item in emailList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 邮件状态 -->
      <el-form-item :label="$t('text_email_status')">
        <el-select
          v-model="searchForm.status"
          :placeholder="$t('text_email') + $t('text_status')"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
        >
          <el-option
            v-for="(v, index) in emailStatusList"
            :key="index"
            :label="$t(v.label)"
            :value="v.value"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 标签 -->
      <el-form-item :label="$t('text_tags')">
        <el-select
          v-model="searchForm.tags"
          multiple
          filterable
          :placeholder="$t('place_select')"
          style="width: 200px"
          clearable
        >
          <el-option
            v-for="item in emailTagsList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 关键词搜索 -->
      <el-form-item :label="$t('text_keyword_search')">
        <el-input
          v-model="searchForm.keyword"
          :placeholder="$t('text_search_title_content')"
          style="width: 200px"
          clearable
        />
      </el-form-item>

      <!-- 提交人搜索 -->
      <el-form-item :label="$t('text_submitter_search')">
        <el-input
          v-model="searchForm.sender_email"
          :placeholder="$t('text_search_email_or_name')"
          style="width: 200px"
          clearable
        />
      </el-form-item>

      <!-- 邮件ID -->
      <el-form-item :label="$t('text_email_id')">
        <el-input
          v-model="searchForm.thread_id"
          :placeholder="$t('text_exact_match')"
          style="width: 200px"
          clearable
        />
      </el-form-item>

      <!-- 处理人 -->
      <el-form-item :label="$t('text_processor')">
        <el-select
          v-model="searchForm.last_reply_service"
          :placeholder="$t('text_processd_by_person')"
          :reserve-keyword="false"
          filterable
          multiple
          clearable
          collapse-tags
        >
          <template #header>
            <el-checkbox
              v-model="checkLast"
              :indeterminate="indeterminateLast"
              @change="lastCheckAll"
              >{{ $t('text_select_all') }}</el-checkbox
            >
          </template>
          <el-option
            v-for="(v, index) in csList"
            :key="index"
            :label="v.account"
            :value="v.account"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 邮件回复时间 -->
      <el-form-item :label="$t('text_email_reply_time')">
        <el-date-picker
          v-model="searchForm.reply_time"
          type="daterange"
          :start-placeholder="$t('text_start_time')"
          :end-placeholder="$t('text_end_time')"
          :range-separator="$t('to')"
          format="MM-DD"
          value-format="MM-DD"
        >
        </el-date-picker>
      </el-form-item>

      <!-- 邮件创建时间 -->
      <el-form-item :label="$t('text_email_create_time')">
        <el-date-picker
          v-model="searchForm.created_time"
          type="daterange"
          :start-placeholder="$t('text_start_time')"
          :end-placeholder="$t('text_end_time')"
          :range-separator="$t('to')"
          format="MM-DD"
          value-format="MM-DD"
        >
        </el-date-picker>
      </el-form-item>

      <!-- 备注信息搜索 -->
      <el-form-item :label="$t('text_remark_search')">
        <el-input
          v-model="searchForm.remark_search"
          :placeholder="$t('text_fuzzy_search')"
          style="width: 200px"
          clearable
        />
      </el-form-item>

      <!-- 操作按钮组 -->
      <SearchFormActions
        :prog-state="progState"
        :progress-num="progressNum"
        :check-email-list="checkEmailList"
        @search="handleSearch"
        @reset="handleReset"
        @download="handleDownload"
        @add-tab="handleAddTab"
        @batch-assign="handleBatchAssign"
      />
    </el-form>
  </el-scrollbar>
</template>

<script setup lang="ts">
import type { CheckboxValueType, FormInstance } from 'element-plus';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import type { SearchForm } from '../types/email';
import SearchFormActions from './SearchFormActions.vue';

interface Props {
  searchForm: SearchForm;
  emailList: Array<{ value: string; label: string }>;
  emailStatusList: Array<{ value: number; label: string }>;
  emailTagsList: Array<{ value: string; label: string }>;
  csList: Array<{ account: string }>;
  progState: boolean;
  progressNum: number;
  checkEmailList: (number | string)[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (event: 'update:searchForm', value: SearchForm): void;
  (event: 'search'): void;
  (event: 'reset'): void;
  (event: 'download'): void;
  (event: 'add-tab'): void;
  (event: 'batch-assign'): void;
}>();

const { t: $t } = useI18n();
const searchFormRef = ref<FormInstance>();

// 处理人全选相关
const checkLast = ref<boolean>(false);
const indeterminateLast = ref<boolean>(false);

watch(
  () => props.searchForm.last_reply_service,
  val => {
    if (val?.length === 0) {
      checkLast.value = false;
      indeterminateLast.value = false;
    } else if (val?.length === props.csList.length) {
      checkLast.value = true;
      indeterminateLast.value = false;
    } else {
      indeterminateLast.value = true;
    }
  }
);

const lastCheckAll = (val: CheckboxValueType) => {
  indeterminateLast.value = false;
  const newSearchForm = { ...props.searchForm };
  if (val) {
    newSearchForm.last_reply_service = props.csList.map(_ => _.account);
  } else {
    newSearchForm.last_reply_service = [];
  }
  emit('update:searchForm', newSearchForm);
};

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  searchFormRef.value?.resetFields();
  emit('reset');
};

const handleDownload = () => {
  emit('download');
};

const handleAddTab = () => {
  emit('add-tab');
};

const handleBatchAssign = () => {
  emit('batch-assign');
};
</script>

<style lang="scss" scoped>
.pane-wapper {
  box-sizing: border-box;
  padding: 10px 5px;

  :deep(.el-input-number) {
    .el-input {
      min-width: 40px;
    }
  }

  :deep(.el-input-number--small) {
    .el-input-number__increase {
      display: none !important;
    }
    .el-input-number__decrease {
      display: none !important;
    }
  }
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 10px;
    margin-right: 0px;
  }
}
</style>
