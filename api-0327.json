{"index": 0, "name": "DCBotConfigService", "add_time": 1743056848, "up_time": 1743056848, "list": [{"query_path": {"path": "/api/dsc_bot_config/add", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["DCBotConfigService"], "_id": 19346, "method": "POST", "title": "新增DC机器人配置", "path": "/api/dsc_bot_config/add", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "67e4efd3ecc87d5a7dec0fe9", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"title\": \"Empty 空对象\",\n  \"$$ref\": \"#/definitions/pbEmpty\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"project\": {\n      \"type\": \"string\",\n      \"title\": \"项目名称 @gotags: validate:\\\"required\\\"\"\n    },\n    \"dsc_name\": {\n      \"type\": \"string\",\n      \"title\": \"DC账号\"\n    },\n    \"bot_desc\": {\n      \"type\": \"string\",\n      \"title\": \"机器人描述\"\n    },\n    \"app_id\": {\n      \"type\": \"string\",\n      \"title\": \"Application ID\"\n    },\n    \"bot_config\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"client_id\": {\n          \"type\": \"string\",\n          \"title\": \"Application ID\"\n        },\n        \"public_key\": {\n          \"type\": \"string\",\n          \"title\": \"Public Key\"\n        },\n        \"bot_token\": {\n          \"type\": \"string\",\n          \"title\": \"Bot Token\"\n        },\n        \"guild_id\": {\n          \"type\": \"string\",\n          \"title\": \"Sever ID\"\n        },\n        \"guild_desc\": {\n          \"type\": \"string\",\n          \"title\": \"服务器名称\"\n        },\n        \"project\": {\n          \"type\": \"string\",\n          \"title\": \"项目名称\"\n        },\n        \"welcome_message\": {\n          \"type\": \"string\",\n          \"title\": \"欢迎消息\"\n        }\n      },\n      \"title\": \"DscBotConfig Discord机器人配置\",\n      \"$$ref\": \"#/definitions/pbDscBotConfig\"\n    },\n    \"user_id\": {\n      \"type\": \"string\",\n      \"title\": \"用户ID @gotags: validate:\\\"required\\\"\"\n    },\n    \"username\": {\n      \"type\": \"string\",\n      \"title\": \"用户名称 @gotags: validate:\\\"required\\\"\"\n    },\n    \"discriminator\": {\n      \"type\": \"string\",\n      \"title\": \"标识 @gotags: validate:\\\"required\\\"\"\n    }\n  },\n  \"title\": \"DCBotConfigAddReq 新增DC机器人配置\",\n  \"$$ref\": \"#/definitions/pbDCBotConfigAddReq\"\n}", "project_id": 526, "catid": 3296, "uid": 395, "add_time": 1743056851, "up_time": 1743056851, "__v": 0}, {"query_path": {"path": "/api/dsc_bot_config/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["DCBotConfigService"], "_id": 19350, "method": "POST", "title": "获取DC机器人配置列表", "path": "/api/dsc_bot_config/list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "67e4efd3ecc87d5a7dec0fef", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"list\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"string\",\n            \"format\": \"uint64\",\n            \"title\": \"ID\"\n          },\n          \"project\": {\n            \"type\": \"string\",\n            \"title\": \"项目名称\"\n          },\n          \"dsc_name\": {\n            \"type\": \"string\",\n            \"title\": \"DC账号\"\n          },\n          \"bot_desc\": {\n            \"type\": \"string\",\n            \"title\": \"机器人描述\"\n          },\n          \"app_id\": {\n            \"type\": \"string\",\n            \"title\": \"Application ID\"\n          },\n          \"bot_config\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"client_id\": {\n                \"type\": \"string\",\n                \"title\": \"Application ID\"\n              },\n              \"public_key\": {\n                \"type\": \"string\",\n                \"title\": \"Public Key\"\n              },\n              \"bot_token\": {\n                \"type\": \"string\",\n                \"title\": \"Bot Token\"\n              },\n              \"guild_id\": {\n                \"type\": \"string\",\n                \"title\": \"Sever ID\"\n              },\n              \"guild_desc\": {\n                \"type\": \"string\",\n                \"title\": \"服务器名称\"\n              },\n              \"project\": {\n                \"type\": \"string\",\n                \"title\": \"项目名称\"\n              },\n              \"welcome_message\": {\n                \"type\": \"string\",\n                \"title\": \"欢迎消息\"\n              }\n            },\n            \"title\": \"DscBotConfig Discord机器人配置\",\n            \"$$ref\": \"#/definitions/pbDscBotConfig\"\n          },\n          \"is_delete\": {\n            \"type\": \"boolean\",\n            \"title\": \"是否删除\"\n          },\n          \"user_id\": {\n            \"type\": \"string\",\n            \"title\": \"用户ID\"\n          },\n          \"username\": {\n            \"type\": \"string\",\n            \"title\": \"用户名称\"\n          },\n          \"discriminator\": {\n            \"type\": \"string\",\n            \"title\": \"标识\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"title\": \"创建时间\"\n          },\n          \"updated_at\": {\n            \"type\": \"string\",\n            \"title\": \"更新时间\"\n          }\n        },\n        \"$$ref\": \"#/definitions/pbDCBotConfigListRespDetail\"\n      }\n    },\n    \"total\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"page\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    }\n  },\n  \"title\": \"DCBotConfigListResp DC机器人配置列表响应\",\n  \"$$ref\": \"#/definitions/pbDCBotConfigListResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"page\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\",\n      \"format\": \"int64\"\n    }\n  },\n  \"title\": \"DCBotConfigListReq DC机器人配置列表请求\",\n  \"$$ref\": \"#/definitions/pbDCBotConfigListReq\"\n}", "project_id": 526, "catid": 3296, "uid": 395, "add_time": 1743056851, "up_time": 1743056851, "__v": 0}]}